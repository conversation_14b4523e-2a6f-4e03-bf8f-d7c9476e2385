{"tests/test_api.py::TestHealthAPI::test_health_check": true, "tests/test_api.py::TestSearchAPI::test_search_basic": true, "tests/test_api.py::TestSearchAPI::test_search_with_pagination": true, "tests/test_api.py::TestSearchAPI::test_search_empty_query": true, "tests/test_api.py::TestSearchAPI::test_search_invalid_page": true, "tests/test_api.py::TestSearchAPI::test_search_invalid_page_size": true, "tests/test_api.py::TestCVEAPI::test_get_cve_detail_not_found": true, "tests/test_api.py::TestSummaryAPI::test_get_summary_not_found": true, "tests/test_api.py::TestSummaryAPI::test_post_summary_not_found": true, "tests/test_api.py::TestCacheAPI::test_get_cache_stats": true, "tests/test_api.py::TestCacheAPI::test_cleanup_cache": true, "tests/test_cache_service.py::TestCacheService::test_get_cached_summary_only_not_found": true, "tests/test_cache_service.py::TestCacheService::test_save_and_get_cached_summary": true, "tests/test_cache_service.py::TestCacheService::test_cache_expiry": true, "tests/test_cache_service.py::TestCacheService::test_get_or_generate_summary_with_cache": true, "tests/test_cache_service.py::TestCacheService::test_get_or_generate_summary_llm_not_configured": true, "tests/test_cache_service.py::TestCacheService::test_log_search": true, "tests/test_cache_service.py::TestCacheService::test_cleanup_expired_cache": true, "tests/test_cache_service.py::TestCacheService::test_get_cache_stats": true, "tests/test_llm_service.py::TestLLMService::test_is_configured_default": true, "tests/test_llm_service.py::TestLLMService::test_call_llm_exception": true, "tests/test_llm_service.py::TestLLMService::test_call_llm_retry_mechanism": true}