["tests/test_api.py::TestCVEAPI::test_get_cve_detail_not_found", "tests/test_api.py::TestCacheAPI::test_cleanup_cache", "tests/test_api.py::TestCacheAPI::test_get_cache_stats", "tests/test_api.py::TestExamplesAPI::test_get_examples", "tests/test_api.py::TestHealthAPI::test_health_check", "tests/test_api.py::TestSearchAPI::test_search_basic", "tests/test_api.py::TestSearchAPI::test_search_empty_query", "tests/test_api.py::TestSearchAPI::test_search_invalid_page", "tests/test_api.py::TestSearchAPI::test_search_invalid_page_size", "tests/test_api.py::TestSearchAPI::test_search_with_pagination", "tests/test_api.py::TestSummaryAPI::test_get_summary_not_found", "tests/test_api.py::TestSummaryAPI::test_post_summary_not_found", "tests/test_cache_service.py::TestCacheService::test_cache_expiry", "tests/test_cache_service.py::TestCacheService::test_cleanup_expired_cache", "tests/test_cache_service.py::TestCacheService::test_get_cache_stats", "tests/test_cache_service.py::TestCacheService::test_get_cached_summary_only_not_found", "tests/test_cache_service.py::TestCacheService::test_get_or_generate_summary_llm_not_configured", "tests/test_cache_service.py::TestCacheService::test_get_or_generate_summary_with_cache", "tests/test_cache_service.py::TestCacheService::test_log_search", "tests/test_cache_service.py::TestCacheService::test_save_and_get_cached_summary", "tests/test_llm_service.py::TestLLMService::test_build_prompt", "tests/test_llm_service.py::TestLLMService::test_build_prompt_minimal_info", "tests/test_llm_service.py::TestLLMService::test_call_llm_empty_response", "tests/test_llm_service.py::TestLLMService::test_call_llm_exception", "tests/test_llm_service.py::TestLLMService::test_call_llm_retry_mechanism", "tests/test_llm_service.py::TestLLMService::test_call_llm_success", "tests/test_llm_service.py::TestLLMService::test_generate_summary_failure", "tests/test_llm_service.py::TestLLMService::test_generate_summary_success", "tests/test_llm_service.py::TestLLMService::test_is_configured_default", "tests/test_llm_service.py::TestLLMService::test_is_configured_with_valid_key", "tests/test_search_engine.py::TestCVESearchEngine::test_parse_cve_file", "tests/test_search_engine.py::TestCVESearchEngine::test_search_basic", "tests/test_search_engine.py::TestCVESearchEngine::test_search_no_results", "tests/test_search_engine.py::TestCVESearchEngine::test_search_pagination", "tests/test_search_engine.py::TestQueryParser::test_parse_complex_query", "tests/test_search_engine.py::TestQueryParser::test_parse_group_term", "tests/test_search_engine.py::TestQueryParser::test_parse_positive_negative", "tests/test_search_engine.py::TestQueryParser::test_parse_quoted_term", "tests/test_search_engine.py::TestQueryParser::test_parse_simple_term"]