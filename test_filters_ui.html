<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>筛选器测试页面</title>
    
    <!-- 外部依赖 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
        }
        .filter-group {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: white;
            overflow: hidden;
            margin-bottom: 20px;
        }
        .filter-group-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: #f8f9fa;
            cursor: pointer;
        }
        .filter-group-header:hover {
            background: #e9ecef;
        }
        .filter-group-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #495057;
        }
        .filter-group-content {
            padding: 20px;
            border-top: 1px solid #e0e0e0;
        }
        .filter-group-content .el-checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        .filter-group-content .el-checkbox {
            margin-right: 0;
            margin-bottom: 10px;
        }
        .expand-icon {
            transition: transform 0.3s;
        }
        .expand-icon.expanded {
            transform: rotate(180deg);
        }
    </style>
</head>
<body>
    <div id="app">
        <h1>筛选器测试页面</h1>
        
        <div class="filter-groups">
            <!-- 漏洞类型 -->
            <div class="filter-group">
                <div class="filter-group-header" @click="toggleFilterGroup('vulnerability')">
                    <span class="filter-group-title">
                        <el-icon><Warning /></el-icon>
                        漏洞类型
                    </span>
                    <el-badge 
                        v-if="getGroupSelectedCount('vulnerability') > 0" 
                        :value="getGroupSelectedCount('vulnerability')" 
                    />
                    <el-icon class="expand-icon" :class="{ expanded: expandedGroups.vulnerability }">
                        <ArrowDown />
                    </el-icon>
                </div>
                <div v-show="expandedGroups.vulnerability" class="filter-group-content">
                    <el-checkbox-group v-model="selectedFilters.vulnerability">
                        <el-checkbox 
                            v-for="filter in filterOptions.vulnerability" 
                            :key="filter.value"
                            :label="filter.value"
                            :title="filter.description"
                        >
                            {{ filter.label }}
                        </el-checkbox>
                    </el-checkbox-group>
                </div>
            </div>

            <!-- 软件类型 -->
            <div class="filter-group">
                <div class="filter-group-header" @click="toggleFilterGroup('software')">
                    <span class="filter-group-title">
                        <el-icon><Monitor /></el-icon>
                        软件类型
                    </span>
                    <el-badge 
                        v-if="getGroupSelectedCount('software') > 0" 
                        :value="getGroupSelectedCount('software')" 
                    />
                    <el-icon class="expand-icon" :class="{ expanded: expandedGroups.software }">
                        <ArrowDown />
                    </el-icon>
                </div>
                <div v-show="expandedGroups.software" class="filter-group-content">
                    <el-checkbox-group v-model="selectedFilters.software">
                        <el-checkbox 
                            v-for="filter in filterOptions.software" 
                            :key="filter.value"
                            :label="filter.value"
                            :title="filter.description"
                        >
                            {{ filter.label }}
                        </el-checkbox>
                    </el-checkbox-group>
                </div>
            </div>

            <!-- 开发语言 -->
            <div class="filter-group">
                <div class="filter-group-header" @click="toggleFilterGroup('language')">
                    <span class="filter-group-title">
                        <el-icon><Document /></el-icon>
                        开发语言
                    </span>
                    <el-badge 
                        v-if="getGroupSelectedCount('language') > 0" 
                        :value="getGroupSelectedCount('language')" 
                    />
                    <el-icon class="expand-icon" :class="{ expanded: expandedGroups.language }">
                        <ArrowDown />
                    </el-icon>
                </div>
                <div v-show="expandedGroups.language" class="filter-group-content">
                    <el-checkbox-group v-model="selectedFilters.language">
                        <el-checkbox 
                            v-for="filter in filterOptions.language" 
                            :key="filter.value"
                            :label="filter.value"
                            :title="filter.description"
                        >
                            {{ filter.label }}
                        </el-checkbox>
                    </el-checkbox-group>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <h3>选中的筛选器:</h3>
            <pre>{{ JSON.stringify(selectedFilters, null, 2) }}</pre>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;
        
        const app = createApp({
            setup() {
                const selectedFilters = ref({
                    vulnerability: [],
                    software: [],
                    language: []
                });

                const expandedGroups = ref({
                    vulnerability: true,
                    software: true,
                    language: true
                });

                const filterOptions = ref({
                    vulnerability: [
                        { label: '缓冲区溢出', value: 'buffer_overflow', description: '包含堆栈或堆缓冲区溢出漏洞' },
                        { label: 'SQL 注入', value: 'sql_injection', description: '包含 SQL 注入相关漏洞' },
                        { label: '任意代码执行', value: 'code_execution', description: '包含远程代码执行漏洞' }
                    ],
                    software: [
                        { label: '服务器软件', value: 'server', description: '包含 Apache、Nginx、Tomcat、IIS 等服务器软件' },
                        { label: '数据库', value: 'database', description: '包含 MySQL、PostgreSQL、MongoDB 等数据库' },
                        { label: '操作系统', value: 'os', description: '包含 Windows、Linux、macOS 等操作系统' }
                    ],
                    language: [
                        { label: 'PHP', value: 'php', description: '包含 PHP 相关漏洞' },
                        { label: 'Java', value: 'java', description: '包含 Java 相关漏洞' },
                        { label: 'Python', value: 'python', description: '包含 Python 相关漏洞' }
                    ]
                });

                const toggleFilterGroup = (groupName) => {
                    expandedGroups.value[groupName] = !expandedGroups.value[groupName];
                };

                const getGroupSelectedCount = (groupName) => {
                    return selectedFilters.value[groupName]?.length || 0;
                };

                return {
                    selectedFilters,
                    expandedGroups,
                    filterOptions,
                    toggleFilterGroup,
                    getGroupSelectedCount
                };
            }
        });

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>
