# CVE 搜索服务

一个基于 FastAPI 的 CVE（Common Vulnerabilities and Exposures）搜索服务，提供现代化的 Web 界面和 LLM 总结功能。

## 功能特性

- 🔍 **高效搜索**: 基于 ripgrep 和 Python 的混合搜索方案
- 🤖 **AI 总结**: 使用 DeepSeek 模型自动总结漏洞信息
- 💾 **智能缓存**: SQLite 缓存 LLM 总结结果，避免重复请求
- 🌐 **现代界面**: 基于 Vue.js 的响应式 Web 界面
- 🚀 **容器化部署**: 完整的 Docker 支持

## 项目结构

```
TestSearchCvesList/
├── app/                    # 主应用目录
│   ├── main.py            # FastAPI 主应用
│   ├── api/               # API 路由
│   ├── core/              # 核心功能
│   ├── models/            # 数据模型
│   ├── services/          # 业务服务
│   └── static/            # 静态文件
├── config/                # 配置文件
├── Dockerfile             # Docker 镜像构建
├── docker-compose.yml     # Docker 编排
└── requirements.txt       # Python 依赖
```

## 快速开始

### 使用 Docker（推荐）

1. 克隆项目并进入目录
2. 配置 LLM 服务（编辑 `config/config.yaml`）
3. 启动服务：
   ```bash
   docker-compose up -d
   ```
4. 访问 http://localhost:8000

### 本地开发

1. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```
2. 配置环境变量或编辑配置文件
3. 启动服务：
   ```bash
   uvicorn app.main:app --reload
   ```

## API 文档

启动服务后访问 http://localhost:8000/docs 查看完整的 API 文档。

### 主要 API 端点

- `GET /api/search` - 搜索 CVE
- `GET /api/cve/{cve_id}` - 获取特定 CVE 详情
- `GET /api/summary/{cve_id}` - 获取 CVE 的 LLM 总结

## 配置说明

编辑 `config/config.yaml` 文件：

```yaml
llm:
  base_url: "https://api.deepseek.com"
  api_key: "your-api-key-here"
  model: "deepseek-chat"

search:
  cve_directory: "~/Github/cvelistV5"
  
database:
  url: "sqlite:///./cache.db"
```

## 搜索语法

支持复杂的搜索语法：

- `term1, term2` - 包含所有词汇
- `+term` - 必须包含
- `-term` - 必须不包含
- `"exact phrase"` - 精确短语
- `prefix(option1|option2)` - 分组搜索

## 开发说明

本项目基于以下技术栈：

- **后端**: FastAPI, SQLAlchemy, LiteLLM
- **前端**: Vue.js 3, Element Plus
- **搜索**: ripgrep + Python 精确匹配
- **数据库**: SQLite
- **容器**: Docker, Docker Compose

## 许可证

MIT License
