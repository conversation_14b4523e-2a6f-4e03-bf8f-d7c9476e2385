#!/usr/bin/env python3
"""
基于混合方案的文件搜索工具
使用 ripgrep 进行粗筛 + Python 精确匹配
"""

import os
import re
import subprocess
import argparse
from typing import List, Set, Optional, Dict, Any
from dataclasses import dataclass
from pathlib import Path


@dataclass
class SearchTerm:
    """搜索项"""
    is_positive: bool
    content: str
    is_quoted: bool = False


@dataclass
class GroupTerm:
    """分组项 (prefix|option1|option2)"""
    prefix: str
    options: List[str]
    is_positive: bool


class QueryParser:
    """查询语法解析器"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        self.pos = 0
        self.text = ""
        self.length = 0
    
    def parse(self, query: str) -> tuple[List[SearchTerm], List[GroupTerm]]:
        """解析查询语句"""
        self.text = query.strip()
        self.length = len(self.text)
        self.pos = 0
        
        search_terms = []
        group_terms = []
        
        while self.pos < self.length:
            self._skip_whitespace()
            if self.pos >= self.length:
                break
                
            # 解析单个搜索项
            term, group = self._parse_term()
            if term:
                search_terms.append(term)
            if group:
                group_terms.append(group)
            
            # 跳过逗号
            self._skip_whitespace()
            if self.pos < self.length and self.text[self.pos] == ',':
                self.pos += 1
        
        return search_terms, group_terms
    
    def _parse_term(self) -> tuple[Optional[SearchTerm], Optional[GroupTerm]]:
        """解析单个搜索项"""
        self._skip_whitespace()
        
        # 判断极性
        is_positive = True
        if self.pos < self.length and self.text[self.pos] in '+-':
            is_positive = self.text[self.pos] == '+'
            self.pos += 1
        
        self._skip_whitespace()
        
        # 解析引号字符串
        if self.pos < self.length and self.text[self.pos] == '"':
            content = self._parse_quoted()
            return SearchTerm(is_positive, content, True), None
        
        # 解析分组或普通词组
        content = self._parse_phrase_or_group()
        
        if '(' in content and ')' in content:
            return None, self._parse_group_content(content, is_positive)
        else:
            # 将下划线转换为空格
            content = content.replace('_', ' ')
            return SearchTerm(is_positive, content, False), None
    
    def _parse_quoted(self) -> str:
        """解析引号内容"""
        self.pos += 1  # 跳过开始引号
        start = self.pos
        
        while self.pos < self.length and self.text[self.pos] != '"':
            self.pos += 1
        
        content = self.text[start:self.pos]
        if self.pos < self.length:
            self.pos += 1  # 跳过结束引号
        
        return content
    
    def _parse_phrase_or_group(self) -> str:
        """解析词组或分组"""
        start = self.pos
        paren_count = 0
        
        while self.pos < self.length:
            char = self.text[self.pos]
            if char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
            elif char in ',+-' and paren_count == 0:
                break
            self.pos += 1
        
        return self.text[start:self.pos].strip()
    
    def _parse_group_content(self, content: str, is_positive: bool) -> GroupTerm:
        """解析分组内容"""
        # 匹配 prefix(option1|option2|...) 格式
        match = re.match(r'([^(]*)\(([^)]+)\)', content)
        if not match:
            # 如果没有前缀，当作普通词组处理
            return GroupTerm("", [content.replace('_', ' ')], is_positive)
        
        prefix = match.group(1).replace('_', ' ').strip()
        options_str = match.group(2)
        options = [opt.replace('_', ' ').strip() for opt in options_str.split('|')]
        
        return GroupTerm(prefix, options, is_positive)
    
    def _skip_whitespace(self):
        """跳过空白字符"""
        while self.pos < self.length and self.text[self.pos].isspace():
            self.pos += 1


class FileSearcher:
    """文件搜索器"""
    
    def __init__(self, base_dir: str):
        self.base_dir = Path(base_dir)
        self.parser = QueryParser()
    
    def search(self, query: str) -> List[str]:
        """执行搜索"""
        print(f"解析查询: {query}")
        
        # 解析查询
        search_terms, group_terms = self.parser.parse(query)
        
        print(f"解析结果:")
        print(f"  普通搜索项: {len(search_terms)}")
        for term in search_terms:
            print(f"    {'包含' if term.is_positive else '排除'}: '{term.content}' {'(引号)' if term.is_quoted else ''}")
        
        print(f"  分组搜索项: {len(group_terms)}")
        for group in group_terms:
            print(f"    {'包含' if group.is_positive else '排除'}: '{group.prefix}' + ({' | '.join(group.options)})")
        
        # 第一阶段：ripgrep 粗筛
        candidate_files = self._ripgrep_filter(search_terms, group_terms)
        print(f"\nripgrep 粗筛结果: {len(candidate_files)} 个候选文件")
        
        if not candidate_files:
            return []
        
        # 第二阶段：Python 精确匹配
        final_results = self._precise_filter(candidate_files, search_terms, group_terms)
        print(f"精确匹配结果: {len(final_results)} 个文件")
        
        return final_results
    
    def _extract_keywords(self, search_terms: List[SearchTerm], group_terms: List[GroupTerm]) -> Set[str]:
        """提取所有关键词用于 ripgrep"""
        keywords = set()
        
        # 从普通搜索项提取
        for term in search_terms:
            if term.is_positive:  # 只用正向词进行粗筛
                if term.is_quoted:
                    keywords.add(term.content)
                else:
                    # 将词组拆分为单词
                    words = term.content.split()
                    keywords.update(words)
        
        # 从分组项提取
        for group in group_terms:
            if group.is_positive:  # 只用正向词进行粗筛
                if group.prefix:
                    keywords.update(group.prefix.split())
                for option in group.options:
                    keywords.update(option.split())
        
        return keywords
    
    def _ripgrep_filter(self, search_terms: List[SearchTerm], group_terms: List[GroupTerm]) -> List[str]:
        """使用 ripgrep 进行粗筛"""
        keywords = self._extract_keywords(search_terms, group_terms)
        
        if not keywords:
            print("警告: 没有找到正向关键词，跳过 ripgrep 筛选")
            return []
        
        print(f"ripgrep 关键词: {list(keywords)}")
        
        try:
            # 构建 ripgrep 命令
            # 使用所有关键词的OR组合进行搜索
            pattern = '|'.join(re.escape(kw) for kw in keywords)
            
            cmd = [
                'rg',
                '--files-with-matches',  # 只返回文件名
                '--ignore-case',         # 忽略大小写
                '--type', 'json',  
                pattern,
                str(self.base_dir)
            ]
            
            print(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                files = [line.strip() for line in result.stdout.strip().split('\n') if line.strip()]
                return files
            elif result.returncode == 1:
                # ripgrep 返回 1 表示没有匹配
                print("ripgrep 没有找到匹配的文件")
                return []
            else:
                print(f"ripgrep 错误 (代码 {result.returncode}): {result.stderr}")
                return []
                
        except subprocess.TimeoutExpired:
            print("ripgrep 执行超时")
            return []
        except FileNotFoundError:
            print("错误: 找不到 ripgrep 命令，请确保已正确安装")
            return []
        except Exception as e:
            print(f"ripgrep 执行出错: {e}")
            return []
    
    def _precise_filter(self, files: List[str], search_terms: List[SearchTerm], 
                       group_terms: List[GroupTerm]) -> List[str]:
        """精确匹配过滤"""
        results = []
        
        for file_path in files:
            try:
                if self._file_matches(file_path, search_terms, group_terms):
                    results.append(file_path)
            except Exception as e:
                print(f"处理文件 {file_path} 时出错: {e}")
                continue
        
        return results
    
    def _file_matches(self, file_path: str, search_terms: List[SearchTerm], 
                     group_terms: List[GroupTerm]) -> bool:
        """检查文件是否匹配所有条件"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read().lower()  # 转为小写进行不区分大小写匹配
        except Exception:
            return False
        
        # 检查普通搜索项
        for term in search_terms:
            search_content = term.content.lower()
            matches = search_content in content
            
            if term.is_positive and not matches:
                return False
            if not term.is_positive and matches:
                return False
        
        # 检查分组项
        for group in group_terms:
            group_matches = False
            
            for option in group.options:
                if group.prefix:
                    full_term = f"{group.prefix} {option}".lower()
                else:
                    full_term = option.lower()
                
                if full_term in content:
                    group_matches = True
                    break
            
            if group.is_positive and not group_matches:
                return False
            if not group.is_positive and group_matches:
                return False
        
        return True


def main():
    parser = argparse.ArgumentParser(description='基于混合方案的文件搜索工具')
    parser.add_argument('directory', help='要搜索的目录')
    parser.add_argument('query', help='搜索查询语句')
    parser.add_argument('--output', '-o', help='将结果保存到文件')
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细信息')
    
    args = parser.parse_args()
    
    # 检查目录是否存在
    if not os.path.isdir(args.directory):
        print(f"错误: 目录不存在: {args.directory}")
        return 1
    
    # 创建搜索器并执行搜索
    searcher = FileSearcher(args.directory)
    results = searcher.search(args.query)
    results.sort(reverse=True)
    
    print(f"\n{'='*50}")
    print(f"搜索完成，共找到 {len(results)} 个匹配的文件:")
    print(f"{'='*50}")
    
    for file_path in results:
        print(file_path)
    
    # 保存结果到文件
    if args.output:
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                for file_path in results:
                    f.write(file_path + '\n')
            print(f"\n结果已保存到: {args.output}")
        except Exception as e:
            print(f"保存文件时出错: {e}")
    
    return 0


if __name__ == '__main__':
    exit(main())
