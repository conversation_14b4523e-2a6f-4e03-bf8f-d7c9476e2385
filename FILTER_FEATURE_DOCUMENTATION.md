# CVE 搜索服务 - 筛选器功能文档

## 功能概述

新增的筛选器功能允许用户通过预定义的分类快速筛选特定类型的CVE漏洞，无需手动输入复杂的搜索语法。

## 功能特性

### 1. 筛选器分组

筛选器按照以下四个主要分组组织：

#### 🔴 漏洞类型
- **缓冲区溢出**: 包含堆栈或堆缓冲区溢出漏洞
- **SQL 注入**: 包含 SQL 注入相关漏洞  
- **任意代码执行**: 包含远程代码执行漏洞
- **任意文件读取**: 包含路径遍历和文件包含漏洞
- **跨站脚本(XSS)**: 包含跨站脚本攻击漏洞
- **跨站请求伪造(CSRF)**: 包含跨站请求伪造漏洞
- **权限提升**: 包含权限提升漏洞
- **信息泄露**: 包含信息泄露漏洞

#### 💻 软件类型
- **服务器软件**: Apache、Nginx、Tomcat、IIS 等服务器软件
- **数据库**: MySQL、PostgreSQL、MongoDB 等数据库
- **操作系统**: Windows、Linux、macOS 等操作系统
- **浏览器**: Chrome、Firefox、Safari、Edge 等浏览器

#### 📝 开发语言
- **PHP**: PHP 相关漏洞
- **Java**: Java 相关漏洞
- **Python**: Python 相关漏洞
- **JavaScript**: JavaScript/Node.js 相关漏洞
- **C/C++**: C/C++ 相关漏洞
- **.NET**: .NET 相关漏洞

#### ⚠️ 严重程度
- **严重 (Critical)**: 严重程度为 Critical 的漏洞
- **高危 (High)**: 严重程度为 High 的漏洞
- **中危 (Medium)**: 严重程度为 Medium 的漏洞
- **低危 (Low)**: 严重程度为 Low 的漏洞

### 2. 用户界面特性

- **可折叠分组**: 每个筛选器分组可以独立展开/收起
- **选择计数**: 显示每个分组和总体的选中项数量
- **快速清空**: 一键清空所有筛选条件
- **智能提示**: 鼠标悬停显示筛选器的详细说明

### 3. 搜索逻辑

#### 查询构建
筛选器会自动转换为相应的搜索语法：

```
缓冲区溢出 → +(heap|stack)_buffer_overflow, +"heap-buffer-overflow", +"stack-buffer-overflow"
SQL注入 → +sql injection, +sql_injection, +SQLi
代码执行 → +remote code execution, +remote_code_execution, +RCE
```

#### 组合搜索
- 用户输入的查询 + 筛选器条件会自动合并
- 支持多个筛选器同时选择
- 不同分组的筛选器使用 OR 逻辑组合
- 同一分组内的多个选项使用 OR 逻辑组合

#### 示例查询
```
用户输入: "apache"
选择筛选器: 缓冲区溢出 + 服务器软件

最终查询: apache, +(heap|stack)_buffer_overflow, +"heap-buffer-overflow", +"stack-buffer-overflow", +apache, +nginx, +tomcat, +iis
```

## 使用方法

### 1. 打开筛选器
- 点击搜索框下方的"显示筛选器"按钮
- 按钮上会显示当前选中的筛选器数量

### 2. 选择筛选条件
- 点击分组标题展开/收起该分组
- 勾选需要的筛选条件
- 可以同时选择多个分组的多个条件

### 3. 执行搜索
- 可以只使用筛选器搜索（不输入文本）
- 也可以结合文本输入和筛选器搜索
- 点击"搜索"按钮执行查询

### 4. 管理筛选器
- 点击"清空筛选"按钮清除所有选择
- 搜索后筛选器面板会自动收起
- 可以随时重新打开调整筛选条件

## 技术实现

### 前端实现
- 使用 Vue.js 3 + Element Plus 构建
- 响应式数据绑定管理筛选器状态
- 自动查询构建和合并逻辑

### 后端兼容
- 完全兼容现有的搜索API
- 无需修改后端代码
- 筛选器生成的查询语法与手动输入等效

### 样式设计
- 响应式布局，支持移动端
- 清晰的视觉层次和交互反馈
- 与现有UI风格保持一致

## 性能优化

- 筛选器状态本地管理，无额外网络请求
- 查询构建在前端完成，减少服务器负载
- 智能的UI更新，避免不必要的重渲染

## 未来扩展

- 可以轻松添加新的筛选器分组和选项
- 支持保存常用筛选器组合
- 可以添加筛选器的统计信息显示
- 支持筛选器的导入/导出功能
