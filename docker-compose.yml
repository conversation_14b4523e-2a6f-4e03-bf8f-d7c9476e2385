version: '3.8'

services:
  cve-search:
    build: .
    container_name: cve-search-service
    ports:
      - "8000:8000"
    volumes:
      # 挂载 CVE 数据库目录（请根据实际路径修改）
      - ~/Github/cvelistV5:/app/cve_data:ro
      # 挂载缓存目录
      - ./cache:/app/cache
      # 挂载配置文件
      - ./config:/app/config
    environment:
      # 可以通过环境变量覆盖配置
      - CVE_DIRECTORY=/app/cve_data
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY:-your-deepseek-api-key-here}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - cve-network

networks:
  cve-network:
    driver: bridge

volumes:
  cache:
    driver: local
