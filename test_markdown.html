<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown 渲染测试</title>
    <script src="https://unpkg.com/marked/marked.min.js"></script>
    <script src="https://unpkg.com/dompurify@2.4.7/dist/purify.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .markdown-output {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>Markdown 渲染测试</h1>
    
    <div class="test-section">
        <h2>测试 1: 基本格式</h2>
        <div id="test1" class="markdown-output"></div>
    </div>
    
    <div class="test-section">
        <h2>测试 2: 列表和代码</h2>
        <div id="test2" class="markdown-output"></div>
    </div>
    
    <div class="test-section">
        <h2>测试 3: CVE 总结示例</h2>
        <div id="test3" class="markdown-output"></div>
    </div>

    <script>
        // 渲染 Markdown 函数
        function renderMarkdown(text) {
            if (!text) return '';
            try {
                marked.setOptions({
                    breaks: true,
                    gfm: true
                });
                
                const html = marked.parse(text);
                return DOMPurify.sanitize(html);
            } catch (error) {
                console.error('Markdown 渲染错误:', error);
                return text.replace(/\n/g, '<br>');
            }
        }

        // 测试数据
        const test1 = `# CVE 漏洞总结

这是一个 **SQL 注入漏洞**，影响了多个系统。

## 主要特点
- 高危漏洞
- 影响范围广
- 需要立即修复`;

        const test2 = `## 漏洞详情

### 影响的系统
1. Web 应用程序
2. 数据库服务器
3. API 接口

### 修复建议
\`\`\`sql
-- 使用参数化查询
SELECT * FROM users WHERE id = ?
\`\`\`

> **重要提示**: 立即更新到最新版本`;

        const test3 = `# CVE-2023-12345 总结

## 漏洞概述
这是一个存在于 Apache Struts 框架中的**远程代码执行漏洞**。

## 技术细节
- **CVSS 评分**: 9.8 (严重)
- **影响版本**: 2.5.30 及更早版本
- **攻击向量**: 网络

## 风险评估
1. **高风险**: 可导致完全系统控制
2. **易利用**: 攻击复杂度低
3. **广泛影响**: 大量应用使用该框架

## 防护措施
- [ ] 立即升级到安全版本
- [ ] 部署 WAF 规则
- [ ] 监控异常访问

---
*生成时间: 2023-06-15*`;

        // 渲染测试
        document.getElementById('test1').innerHTML = renderMarkdown(test1);
        document.getElementById('test2').innerHTML = renderMarkdown(test2);
        document.getElementById('test3').innerHTML = renderMarkdown(test3);
    </script>
</body>
</html>
