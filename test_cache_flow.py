#!/usr/bin/env python3
"""
测试缓存完整流程
验证 /api/summary 和 /api/search 之间的缓存一致性
"""

import requests
import json
import time

def test_cache_flow():
    base_url = "http://localhost:8001"
    
    print("🔍 测试缓存完整流程")
    print("=" * 50)
    
    # 步骤 1: 搜索一个 CVE
    print("步骤 1: 搜索 CVE...")
    search_response = requests.get(f"{base_url}/api/search?query=buffer+overflow&page=1&page_size=1")
    
    if search_response.status_code != 200:
        print(f"❌ 搜索失败: {search_response.status_code}")
        return
    
    search_data = search_response.json()
    if not search_data['results']:
        print("❌ 没有搜索结果")
        return
    
    cve_id = search_data['results'][0]['cve_info']['cve_id']
    has_summary_before = search_data['results'][0].get('summary') is not None
    
    print(f"✅ 找到 CVE: {cve_id}")
    print(f"📝 搜索时是否有总结: {'是' if has_summary_before else '否'}")
    
    # 步骤 2: 生成总结
    print(f"\n步骤 2: 为 {cve_id} 生成总结...")
    summary_response = requests.get(f"{base_url}/api/summary/{cve_id}")
    
    if summary_response.status_code != 200:
        print(f"❌ 总结生成失败: {summary_response.status_code}")
        return
    
    summary_data = summary_response.json()
    print(f"✅ 总结生成成功")
    print(f"🤖 模型: {summary_data['model_used']}")
    print(f"📏 总结长度: {len(summary_data['summary'])} 字符")
    print(f"🔑 缓存的 CVE ID: {summary_data['cve_id']}")
    
    # 步骤 3: 再次搜索相同内容
    print(f"\n步骤 3: 再次搜索相同内容...")
    search2_response = requests.get(f"{base_url}/api/search?query=buffer+overflow&page=1&page_size=1")
    
    if search2_response.status_code != 200:
        print(f"❌ 第二次搜索失败: {search2_response.status_code}")
        return
    
    search2_data = search2_response.json()
    if not search2_data['results']:
        print("❌ 第二次搜索没有结果")
        return
    
    cve_id_2 = search2_data['results'][0]['cve_info']['cve_id']
    has_summary_after = search2_data['results'][0].get('summary') is not None
    
    print(f"✅ 第二次搜索完成")
    print(f"🔍 找到的 CVE: {cve_id_2}")
    print(f"📝 是否有总结: {'是' if has_summary_after else '否'}")
    
    # 步骤 4: 验证缓存一致性
    print(f"\n步骤 4: 验证缓存一致性...")
    
    if cve_id != cve_id_2:
        print(f"❌ CVE ID 不一致!")
        print(f"   第一次搜索: {cve_id}")
        print(f"   第二次搜索: {cve_id_2}")
        print("   这说明搜索结果不稳定，可能是排序问题")
        return
    
    print(f"✅ CVE ID 一致: {cve_id}")
    
    if not has_summary_after:
        print(f"❌ 缓存失效! 第二次搜索时没有找到总结")
        print("   这说明 /api/summary 生成的总结没有被 /api/search 正确获取")
        return
    
    # 比较总结内容
    if has_summary_after:
        cached_summary = search2_data['results'][0]['summary']['summary']
        if cached_summary == summary_data['summary']:
            print(f"✅ 总结内容一致!")
            print(f"📝 总结预览: {cached_summary[:100]}...")
        else:
            print(f"❌ 总结内容不一致!")
            print(f"   原始总结长度: {len(summary_data['summary'])}")
            print(f"   缓存总结长度: {len(cached_summary)}")
    
    # 步骤 5: 测试精确 CVE ID 搜索
    print(f"\n步骤 5: 测试精确 CVE ID 搜索...")
    exact_search_response = requests.get(f"{base_url}/api/search?query={cve_id}&page=1&page_size=1")
    
    if exact_search_response.status_code != 200:
        print(f"❌ 精确搜索失败: {exact_search_response.status_code}")
        return
    
    exact_search_data = exact_search_response.json()
    if not exact_search_data['results']:
        print("❌ 精确搜索没有结果")
        return
    
    exact_cve_id = exact_search_data['results'][0]['cve_info']['cve_id']
    exact_has_summary = exact_search_data['results'][0].get('summary') is not None
    
    print(f"✅ 精确搜索完成")
    print(f"🔍 找到的 CVE: {exact_cve_id}")
    print(f"📝 是否有总结: {'是' if exact_has_summary else '否'}")
    
    if exact_cve_id == cve_id and exact_has_summary:
        print(f"✅ 精确搜索缓存一致性验证通过!")
    else:
        print(f"❌ 精确搜索缓存一致性验证失败!")
    
    print(f"\n🎉 缓存流程测试完成!")
    
    # 总结
    print(f"\n📋 测试总结:")
    print(f"✅ CVE 搜索: {cve_id}")
    print(f"✅ 总结生成: {'成功' if summary_response.status_code == 200 else '失败'}")
    print(f"✅ 缓存一致性: {'通过' if has_summary_after else '失败'}")
    print(f"✅ 精确搜索: {'通过' if exact_has_summary else '失败'}")

if __name__ == "__main__":
    test_cache_flow()
