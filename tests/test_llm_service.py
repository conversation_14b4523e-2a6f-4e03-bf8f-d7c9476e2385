"""
LLM 服务测试
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock

from app.services.llm_service import LLMService
from app.models.schemas import CVEInfo, CVESummary


class TestLLMService:
    """LLM 服务测试"""
    
    @pytest.fixture
    def llm_service(self):
        """创建 LLM 服务实例"""
        return LLMService()
    
    @pytest.fixture
    def sample_cve_info(self):
        """创建示例 CVE 信息"""
        return CVEInfo(
            cve_id="CVE-2023-TEST",
            title="Test SQL Injection Vulnerability",
            description="A SQL injection vulnerability exists in the web application",
            published_date="2023-01-01T00:00:00.000Z",
            modified_date="2023-01-02T00:00:00.000Z",
            severity="HIGH",
            cvss_score=7.5,
            affected_products=["Web Application v1.0"],
            references=["https://example.com/advisory"],
            file_path="/test/path"
        )
    
    def test_is_configured_default(self, llm_service):
        """测试默认配置状态"""
        # 默认配置应该是未配置状态
        assert not llm_service.is_configured()
    
    def test_is_configured_with_valid_key(self, llm_service):
        """测试有效配置"""
        llm_service.api_key = "valid-api-key"
        assert llm_service.is_configured()
    
    def test_build_prompt(self, llm_service, sample_cve_info):
        """测试构建提示词"""
        prompt = llm_service._build_prompt(sample_cve_info)
        
        assert sample_cve_info.cve_id in prompt
        assert sample_cve_info.title in prompt
        assert sample_cve_info.description in prompt
        assert str(sample_cve_info.cvss_score) in prompt
        assert sample_cve_info.severity in prompt
        assert sample_cve_info.affected_products[0] in prompt
        assert "中文总结" in prompt
    
    def test_build_prompt_minimal_info(self, llm_service):
        """测试最小信息构建提示词"""
        minimal_cve = CVEInfo(
            cve_id="CVE-2023-MINIMAL",
            title=None,
            description=None,
            published_date=None,
            modified_date=None,
            severity=None,
            cvss_score=None,
            affected_products=[],
            references=[],
            file_path="/test/path"
        )
        
        prompt = llm_service._build_prompt(minimal_cve)
        assert minimal_cve.cve_id in prompt
        assert "中文总结" in prompt
    
    @pytest.mark.asyncio
    async def test_call_llm_success(self, llm_service):
        """测试成功调用 LLM"""
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = "这是一个测试总结"
        
        with patch('app.services.llm_service.acompletion', return_value=mock_response):
            result = await llm_service._call_llm("test prompt")
            assert result == "这是一个测试总结"
    
    @pytest.mark.asyncio
    async def test_call_llm_empty_response(self, llm_service):
        """测试 LLM 返回空响应"""
        mock_response = MagicMock()
        mock_response.choices = []
        
        with patch('app.services.llm_service.acompletion', return_value=mock_response):
            result = await llm_service._call_llm("test prompt")
            assert result is None
    
    @pytest.mark.asyncio
    async def test_call_llm_exception(self, llm_service):
        """测试 LLM 调用异常"""
        with patch('app.services.llm_service.acompletion', side_effect=Exception("API Error")):
            result = await llm_service._call_llm("test prompt")
            assert result is None
    
    @pytest.mark.asyncio
    async def test_call_llm_retry_mechanism(self, llm_service):
        """测试重试机制"""
        # 设置较少的重试次数以加快测试
        llm_service.max_retries = 2
        
        call_count = 0
        def mock_acompletion(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count < 2:
                raise Exception("Temporary error")
            
            mock_response = MagicMock()
            mock_response.choices = [MagicMock()]
            mock_response.choices[0].message.content = "成功响应"
            return mock_response
        
        with patch('app.services.llm_service.acompletion', side_effect=mock_acompletion):
            with patch('asyncio.sleep'):  # 跳过实际等待
                result = await llm_service._call_llm("test prompt")
                assert result == "成功响应"
                assert call_count == 2
    
    @pytest.mark.asyncio
    async def test_generate_summary_success(self, llm_service, sample_cve_info):
        """测试成功生成总结"""
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = "这是一个SQL注入漏洞的总结"
        
        with patch('app.services.llm_service.acompletion', return_value=mock_response):
            result = await llm_service.generate_summary(sample_cve_info)
            
            assert result is not None
            assert isinstance(result, CVESummary)
            assert result.cve_id == sample_cve_info.cve_id
            assert result.summary == "这是一个SQL注入漏洞的总结"
            assert result.model_used == llm_service.model
    
    @pytest.mark.asyncio
    async def test_generate_summary_failure(self, llm_service, sample_cve_info):
        """测试生成总结失败"""
        with patch('app.services.llm_service.acompletion', side_effect=Exception("API Error")):
            result = await llm_service.generate_summary(sample_cve_info)
            assert result is None
