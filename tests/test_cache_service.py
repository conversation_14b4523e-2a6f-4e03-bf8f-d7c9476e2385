"""
缓存服务测试
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch

from app.services.cache_service import CacheService
from app.models.schemas import CVEInfo, CVESummary
from app.core.database import CVESummaryCache


class TestCacheService:
    """缓存服务测试"""
    
    @pytest.fixture
    def cache_service(self):
        """创建缓存服务实例"""
        return CacheService()
    
    @pytest.fixture
    def sample_cve_info(self):
        """创建示例 CVE 信息"""
        return CVEInfo(
            cve_id="CVE-2023-TEST",
            title="Test CVE",
            description="Test description",
            published_date="2023-01-01T00:00:00.000Z",
            modified_date="2023-01-02T00:00:00.000Z",
            severity="HIGH",
            cvss_score=7.5,
            affected_products=["Test Product"],
            references=["https://example.com"],
            file_path="/test/path"
        )
    
    @pytest.fixture
    def sample_summary(self):
        """创建示例总结"""
        return CVESummary(
            cve_id="CVE-2023-TEST",
            summary="Test summary content",
            generated_at=datetime.utcnow(),
            model_used="test-model"
        )
    
    @pytest.mark.asyncio
    async def test_get_cached_summary_only_not_found(self, cache_service, test_db):
        """测试获取不存在的缓存总结"""
        async with test_db() as db:
            result = await cache_service.get_cached_summary_only("CVE-NOTFOUND", db)
            assert result is None
    
    @pytest.mark.asyncio
    async def test_save_and_get_cached_summary(self, cache_service, test_db, sample_summary):
        """测试保存和获取缓存总结"""
        async with test_db() as db:
            # 保存总结
            await cache_service._save_summary_to_cache(sample_summary, db)
            
            # 获取总结
            result = await cache_service.get_cached_summary_only(sample_summary.cve_id, db)
            assert result is not None
            assert result.cve_id == sample_summary.cve_id
            assert result.summary == sample_summary.summary
            assert result.model_used == sample_summary.model_used
    
    @pytest.mark.asyncio
    async def test_cache_expiry(self, cache_service, test_db, sample_summary):
        """测试缓存过期"""
        # 修改缓存 TTL 为很短的时间
        original_ttl = cache_service.cache_ttl
        cache_service.cache_ttl = 1  # 1 秒
        
        try:
            async with test_db() as db:
                # 保存总结
                await cache_service._save_summary_to_cache(sample_summary, db)
                
                # 立即获取应该成功
                result = await cache_service.get_cached_summary_only(sample_summary.cve_id, db)
                assert result is not None
                
                # 等待过期
                import asyncio
                await asyncio.sleep(2)
                
                # 再次获取应该返回 None（已过期）
                result = await cache_service._get_cached_summary(sample_summary.cve_id, db)
                assert result is None
        finally:
            cache_service.cache_ttl = original_ttl
    
    @pytest.mark.asyncio
    async def test_get_or_generate_summary_with_cache(self, cache_service, test_db, sample_cve_info, sample_summary):
        """测试从缓存获取总结"""
        async with test_db() as db:
            # 先保存到缓存
            await cache_service._save_summary_to_cache(sample_summary, db)
            
            # 获取总结（应该从缓存获取）
            result = await cache_service.get_or_generate_summary(sample_cve_info, db)
            assert result is not None
            assert result.cve_id == sample_summary.cve_id
    
    @pytest.mark.asyncio
    async def test_get_or_generate_summary_llm_not_configured(self, cache_service, test_db, sample_cve_info):
        """测试 LLM 未配置时的行为"""
        async with test_db() as db:
            # 模拟 LLM 未配置
            with patch.object(cache_service.llm_service, 'is_configured', return_value=False):
                result = await cache_service.get_or_generate_summary(sample_cve_info, db)
                assert result is None
    
    @pytest.mark.asyncio
    async def test_log_search(self, cache_service, test_db):
        """测试记录搜索日志"""
        async with test_db() as db:
            await cache_service.log_search("test query", 10, 1.5, db)
            # 如果没有异常，说明记录成功
    
    @pytest.mark.asyncio
    async def test_cleanup_expired_cache(self, cache_service, test_db, sample_summary):
        """测试清理过期缓存"""
        # 修改缓存 TTL
        original_ttl = cache_service.cache_ttl
        cache_service.cache_ttl = 1
        
        try:
            async with test_db() as db:
                # 保存总结
                await cache_service._save_summary_to_cache(sample_summary, db)
                
                # 等待过期
                import asyncio
                await asyncio.sleep(2)
                
                # 清理过期缓存
                deleted_count = await cache_service.cleanup_expired_cache(db)
                assert deleted_count >= 0
        finally:
            cache_service.cache_ttl = original_ttl
    
    @pytest.mark.asyncio
    async def test_get_cache_stats(self, cache_service, test_db):
        """测试获取缓存统计"""
        async with test_db() as db:
            stats = await cache_service.get_cache_stats(db)
            assert "total_cached" in stats
            assert "expired_cached" in stats
            assert "valid_cached" in stats
            assert "cache_ttl_hours" in stats
            assert isinstance(stats["total_cached"], int)
            assert isinstance(stats["expired_cached"], int)
            assert isinstance(stats["valid_cached"], int)
            assert isinstance(stats["cache_ttl_hours"], (int, float))
