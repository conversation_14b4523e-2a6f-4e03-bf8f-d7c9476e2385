"""
测试配置文件
"""

import pytest
import asyncio
import tempfile
import os
from pathlib import Path
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from app.main import app
from app.core.database import Base, get_database
from app.core.config import settings


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def test_db():
    """创建测试数据库"""
    # 创建临时数据库文件
    db_fd, db_path = tempfile.mkstemp(suffix='.db')
    os.close(db_fd)
    
    # 创建测试引擎
    test_engine = create_async_engine(
        f"sqlite+aiosqlite:///{db_path}",
        echo=False
    )
    
    # 创建表
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # 创建会话工厂
    TestSessionLocal = sessionmaker(
        test_engine, 
        class_=AsyncSession, 
        expire_on_commit=False
    )
    
    yield TestSessionLocal
    
    # 清理
    await test_engine.dispose()
    os.unlink(db_path)


@pytest.fixture
def test_cve_data():
    """创建测试 CVE 数据目录"""
    with tempfile.TemporaryDirectory() as temp_dir:
        cve_dir = Path(temp_dir) / "cves" / "2023"
        cve_dir.mkdir(parents=True)
        
        # 创建测试 CVE 文件
        test_cve = {
            "cveMetadata": {
                "cveId": "CVE-2023-TEST",
                "datePublished": "2023-01-01T00:00:00.000Z",
                "dateUpdated": "2023-01-02T00:00:00.000Z"
            },
            "containers": {
                "cna": {
                    "descriptions": [
                        {
                            "lang": "en",
                            "value": "Test vulnerability description"
                        }
                    ],
                    "affected": [
                        {
                            "vendor": "Test Vendor",
                            "product": "Test Product"
                        }
                    ],
                    "metrics": [
                        {
                            "cvssV3_1": {
                                "baseScore": 7.5,
                                "baseSeverity": "HIGH"
                            }
                        }
                    ]
                }
            }
        }
        
        import json
        with open(cve_dir / "CVE-2023-TEST.json", "w") as f:
            json.dump(test_cve, f)
        
        yield temp_dir


@pytest.fixture
def client(test_db, test_cve_data):
    """创建测试客户端"""
    async def override_get_database():
        async with test_db() as session:
            yield session
    
    # 覆盖依赖
    app.dependency_overrides[get_database] = override_get_database
    
    # 临时修改 CVE 目录设置
    original_cve_dir = settings.search.cve_directory
    settings.search.cve_directory = test_cve_data
    
    with TestClient(app) as test_client:
        yield test_client
    
    # 恢复设置
    settings.search.cve_directory = original_cve_dir
    app.dependency_overrides.clear()
