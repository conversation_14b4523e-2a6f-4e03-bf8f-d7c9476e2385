"""
API 接口测试
"""

import pytest
from fastapi.testclient import TestClient


class TestHealthAPI:
    """健康检查 API 测试"""
    
    def test_health_check(self, client: TestClient):
        """测试健康检查接口"""
        response = client.get("/api/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "version" in data
        assert "cve_directory_exists" in data
        assert "database_connected" in data


class TestSearchAPI:
    """搜索 API 测试"""
    
    def test_search_basic(self, client: TestClient):
        """测试基本搜索功能"""
        response = client.get("/api/search?query=test")
        assert response.status_code == 200
        
        data = response.json()
        assert "query" in data
        assert "total_results" in data
        assert "page" in data
        assert "page_size" in data
        assert "total_pages" in data
        assert "results" in data
        assert "search_time" in data
        
        assert data["query"] == "test"
        assert data["page"] == 1
        assert data["page_size"] == 50
    
    def test_search_with_pagination(self, client: TestClient):
        """测试分页搜索"""
        response = client.get("/api/search?query=test&page=1&page_size=10")
        assert response.status_code == 200
        
        data = response.json()
        assert data["page"] == 1
        assert data["page_size"] == 10
    
    def test_search_empty_query(self, client: TestClient):
        """测试空查询"""
        response = client.get("/api/search?query=")
        assert response.status_code == 422  # Validation error
    
    def test_search_invalid_page(self, client: TestClient):
        """测试无效页码"""
        response = client.get("/api/search?query=test&page=0")
        assert response.status_code == 422  # Validation error
    
    def test_search_invalid_page_size(self, client: TestClient):
        """测试无效页面大小"""
        response = client.get("/api/search?query=test&page_size=0")
        assert response.status_code == 422  # Validation error
        
        response = client.get("/api/search?query=test&page_size=101")
        assert response.status_code == 422  # Validation error


class TestCVEAPI:
    """CVE 详情 API 测试"""
    
    def test_get_cve_detail_not_found(self, client: TestClient):
        """测试获取不存在的 CVE"""
        response = client.get("/api/cve/CVE-9999-NOTFOUND")
        assert response.status_code == 404


class TestSummaryAPI:
    """总结 API 测试"""
    
    def test_get_summary_not_found(self, client: TestClient):
        """测试获取不存在 CVE 的总结"""
        response = client.get("/api/summary/CVE-9999-NOTFOUND")
        assert response.status_code == 404
    
    def test_post_summary_not_found(self, client: TestClient):
        """测试 POST 方式获取不存在 CVE 的总结"""
        response = client.post("/api/summary", json={
            "cve_id": "CVE-9999-NOTFOUND",
            "force_refresh": False
        })
        assert response.status_code == 404


class TestExamplesAPI:
    """搜索示例 API 测试"""
    
    def test_get_examples(self, client: TestClient):
        """测试获取搜索示例"""
        response = client.get("/api/examples")
        assert response.status_code == 200
        
        data = response.json()
        assert "examples" in data
        assert isinstance(data["examples"], list)
        assert len(data["examples"]) > 0
        
        # 检查示例结构
        example = data["examples"][0]
        assert "title" in example
        assert "query" in example
        assert "description" in example


class TestCacheAPI:
    """缓存 API 测试"""
    
    def test_get_cache_stats(self, client: TestClient):
        """测试获取缓存统计"""
        response = client.get("/api/cache/stats")
        assert response.status_code == 200
        
        data = response.json()
        assert "total_cached" in data
        assert "expired_cached" in data
        assert "valid_cached" in data
        assert "cache_ttl_hours" in data
    
    def test_cleanup_cache(self, client: TestClient):
        """测试清理缓存"""
        response = client.post("/api/cache/cleanup")
        assert response.status_code == 200
        
        data = response.json()
        assert "message" in data
