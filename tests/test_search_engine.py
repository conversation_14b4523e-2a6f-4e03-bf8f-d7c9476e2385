"""
搜索引擎测试
"""

import pytest
import tempfile
import json
from pathlib import Path

from app.core.search_engine import <PERSON><PERSON>SearchE<PERSON><PERSON>, QueryParser, SearchTerm, GroupTerm


class TestQueryParser:
    """查询解析器测试"""
    
    def setup_method(self):
        self.parser = QueryParser()
    
    def test_parse_simple_term(self):
        """测试解析简单词汇"""
        terms, groups = self.parser.parse("test")
        assert len(terms) == 1
        assert len(groups) == 0
        assert terms[0].content == "test"
        assert terms[0].is_positive == True
        assert terms[0].is_quoted == False
    
    def test_parse_quoted_term(self):
        """测试解析引号词汇"""
        terms, groups = self.parser.parse('"buffer overflow"')
        assert len(terms) == 1
        assert terms[0].content == "buffer overflow"
        assert terms[0].is_quoted == True
    
    def test_parse_positive_negative(self):
        """测试解析正负词汇"""
        terms, groups = self.parser.parse("+include, -exclude")
        assert len(terms) == 2
        assert terms[0].content == "include"
        assert terms[0].is_positive == True
        assert terms[1].content == "exclude"
        assert terms[1].is_positive == False
    
    def test_parse_group_term(self):
        """测试解析分组词汇"""
        terms, groups = self.parser.parse("microsoft(windows|office)")
        assert len(terms) == 0
        assert len(groups) == 1
        assert groups[0].prefix == "microsoft"
        assert groups[0].options == ["windows", "office"]
        assert groups[0].is_positive == True
    
    def test_parse_complex_query(self):
        """测试解析复杂查询"""
        terms, groups = self.parser.parse('+sql injection, -"denial of service", apache(httpd|tomcat)')
        assert len(terms) == 2
        assert len(groups) == 1
        
        # 检查正向词汇
        assert terms[0].content == "sql injection"
        assert terms[0].is_positive == True
        
        # 检查负向词汇
        assert terms[1].content == "denial of service"
        assert terms[1].is_positive == False
        assert terms[1].is_quoted == True
        
        # 检查分组
        assert groups[0].prefix == "apache"
        assert groups[0].options == ["httpd", "tomcat"]


class TestCVESearchEngine:
    """CVE 搜索引擎测试"""
    
    @pytest.fixture
    def search_engine(self):
        """创建测试搜索引擎"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试 CVE 数据
            cve_dir = Path(temp_dir) / "cves" / "2023"
            cve_dir.mkdir(parents=True)
            
            # 创建测试 CVE 文件
            test_cves = [
                {
                    "cveMetadata": {
                        "cveId": "CVE-2023-0001",
                        "datePublished": "2023-01-01T00:00:00.000Z"
                    },
                    "containers": {
                        "cna": {
                            "descriptions": [
                                {
                                    "lang": "en",
                                    "value": "SQL injection vulnerability in web application"
                                }
                            ],
                            "affected": [
                                {
                                    "vendor": "Test Corp",
                                    "product": "Web App"
                                }
                            ]
                        }
                    }
                },
                {
                    "cveMetadata": {
                        "cveId": "CVE-2023-0002",
                        "datePublished": "2023-01-02T00:00:00.000Z"
                    },
                    "containers": {
                        "cna": {
                            "descriptions": [
                                {
                                    "lang": "en",
                                    "value": "Buffer overflow in network service"
                                }
                            ],
                            "affected": [
                                {
                                    "vendor": "Network Inc",
                                    "product": "Service Daemon"
                                }
                            ]
                        }
                    }
                }
            ]
            
            for i, cve_data in enumerate(test_cves):
                with open(cve_dir / f"CVE-2023-000{i+1}.json", "w") as f:
                    json.dump(cve_data, f)
            
            yield CVESearchEngine(temp_dir)
    
    @pytest.mark.asyncio
    async def test_search_basic(self, search_engine):
        """测试基本搜索功能"""
        results, total = await search_engine.search("vulnerability")
        assert total >= 0
        assert len(results) <= total
    
    @pytest.mark.asyncio
    async def test_search_pagination(self, search_engine):
        """测试分页搜索"""
        # 第一页
        results1, total = await search_engine.search("CVE", page=1, page_size=1)
        
        if total > 1:
            # 第二页
            results2, total2 = await search_engine.search("CVE", page=2, page_size=1)
            
            assert total == total2  # 总数应该相同
            assert len(results1) == 1
            assert len(results2) == 1
            
            # 结果应该不同
            if len(results1) > 0 and len(results2) > 0:
                assert results1[0].cve_id != results2[0].cve_id
    
    @pytest.mark.asyncio
    async def test_search_no_results(self, search_engine):
        """测试无结果搜索"""
        results, total = await search_engine.search("nonexistent_term_12345")
        assert total == 0
        assert len(results) == 0
    
    @pytest.mark.asyncio
    async def test_parse_cve_file(self, search_engine):
        """测试 CVE 文件解析"""
        # 创建临时 CVE 文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            test_cve = {
                "cveMetadata": {
                    "cveId": "CVE-2023-TEST",
                    "datePublished": "2023-01-01T00:00:00.000Z"
                },
                "containers": {
                    "cna": {
                        "descriptions": [
                            {
                                "lang": "en",
                                "value": "Test description"
                            }
                        ],
                        "affected": [
                            {
                                "vendor": "Test",
                                "product": "Product"
                            }
                        ],
                        "metrics": [
                            {
                                "cvssV3_1": {
                                    "baseScore": 7.5,
                                    "baseSeverity": "HIGH"
                                }
                            }
                        ]
                    }
                }
            }
            json.dump(test_cve, f)
            temp_file = f.name
        
        try:
            cve_info = await search_engine._parse_cve_file(temp_file)
            assert cve_info is not None
            assert cve_info.cve_id == "CVE-2023-TEST"
            assert cve_info.description == "Test description"
            assert cve_info.severity == "HIGH"
            assert cve_info.cvss_score == 7.5
            assert "Test Product" in cve_info.affected_products
        finally:
            import os
            os.unlink(temp_file)
