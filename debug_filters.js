// 调试筛选器功能的脚本
// 在浏览器控制台中运行

console.log('=== 筛选器调试信息 ===');

// 检查Vue应用实例
if (window.Vue && window.Vue.version) {
    console.log('✅ Vue版本:', window.Vue.version);
} else {
    console.log('❌ Vue未加载');
}

// 检查Element Plus
if (window.ElementPlus) {
    console.log('✅ Element Plus已加载');
} else {
    console.log('❌ Element Plus未加载');
}

// 检查应用挂载
const appElement = document.getElementById('app');
if (appElement && appElement.__vue_app__) {
    console.log('✅ Vue应用已挂载');
    
    // 获取应用实例
    const app = appElement.__vue_app__;
    const instance = app._instance;
    
    if (instance && instance.setupState) {
        const state = instance.setupState;
        
        console.log('📊 应用状态:');
        console.log('  showFilters:', state.showFilters);
        console.log('  expandedGroups:', state.expandedGroups);
        console.log('  selectedFilters:', state.selectedFilters);
        console.log('  filterOptions:', state.filterOptions);
        
        // 检查筛选器选项数量
        if (state.filterOptions) {
            console.log('📋 筛选器选项数量:');
            Object.keys(state.filterOptions).forEach(key => {
                const options = state.filterOptions[key];
                console.log(`  ${key}: ${options ? options.length : 0} 个选项`);
            });
        }
        
        // 检查函数是否存在
        const functions = [
            'toggleFilters',
            'toggleFilterGroup', 
            'getSelectedFiltersCount',
            'getGroupSelectedCount',
            'clearAllFilters'
        ];
        
        console.log('🔧 筛选器函数:');
        functions.forEach(funcName => {
            if (typeof state[funcName] === 'function') {
                console.log(`  ✅ ${funcName}`);
            } else {
                console.log(`  ❌ ${funcName} 缺失`);
            }
        });
        
    } else {
        console.log('❌ 无法访问应用状态');
    }
} else {
    console.log('❌ Vue应用未挂载');
}

// 检查筛选器DOM元素
const filtersSection = document.querySelector('.filters-section');
if (filtersSection) {
    console.log('✅ 筛选器DOM元素存在');
    console.log('  显示状态:', filtersSection.style.display !== 'none' ? '显示' : '隐藏');
} else {
    console.log('❌ 筛选器DOM元素不存在');
}

// 检查筛选器组
const filterGroups = document.querySelectorAll('.filter-group');
console.log(`📦 找到 ${filterGroups.length} 个筛选器组`);

filterGroups.forEach((group, index) => {
    const title = group.querySelector('.filter-group-title');
    const content = group.querySelector('.filter-group-content');
    const checkboxes = group.querySelectorAll('.el-checkbox');
    
    console.log(`  组 ${index + 1}:`);
    console.log(`    标题: ${title ? title.textContent.trim() : '未找到'}`);
    console.log(`    内容显示: ${content && content.style.display !== 'none' ? '显示' : '隐藏'}`);
    console.log(`    复选框数量: ${checkboxes.length}`);
});

console.log('=== 调试完成 ===');

// 提供一些有用的调试函数
window.debugFilters = {
    showFilters: () => {
        const app = document.getElementById('app').__vue_app__._instance;
        if (app && app.setupState) {
            app.setupState.showFilters = true;
            console.log('筛选器已显示');
        }
    },
    
    expandAllGroups: () => {
        const app = document.getElementById('app').__vue_app__._instance;
        if (app && app.setupState) {
            Object.keys(app.setupState.expandedGroups).forEach(key => {
                app.setupState.expandedGroups[key] = true;
            });
            console.log('所有分组已展开');
        }
    },
    
    getState: () => {
        const app = document.getElementById('app').__vue_app__._instance;
        if (app && app.setupState) {
            return {
                showFilters: app.setupState.showFilters,
                expandedGroups: app.setupState.expandedGroups,
                selectedFilters: app.setupState.selectedFilters,
                filterOptionsCount: Object.keys(app.setupState.filterOptions).reduce((acc, key) => {
                    acc[key] = app.setupState.filterOptions[key].length;
                    return acc;
                }, {})
            };
        }
        return null;
    }
};

console.log('💡 调试函数已添加到 window.debugFilters');
console.log('  - debugFilters.showFilters() - 显示筛选器');
console.log('  - debugFilters.expandAllGroups() - 展开所有分组');
console.log('  - debugFilters.getState() - 获取当前状态');
