#!/usr/bin/env python3
"""
手动测试脚本
验证主要功能是否正常工作
"""

import asyncio
import httpx
import json


async def test_api():
    """测试 API 功能"""
    base_url = "http://localhost:8001"
    
    async with httpx.AsyncClient() as client:
        print("🔍 测试 CVE 搜索服务")
        print("=" * 50)
        
        # 1. 测试健康检查
        print("1. 测试健康检查...")
        try:
            response = await client.get(f"{base_url}/api/health")
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ 健康检查通过: {data['status']}")
                print(f"   📁 CVE 目录存在: {data['cve_directory_exists']}")
                print(f"   🗄️ 数据库连接: {data['database_connected']}")
            else:
                print(f"   ❌ 健康检查失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 健康检查异常: {e}")
        
        print()
        
        # 2. 测试搜索示例
        print("2. 测试搜索示例...")
        try:
            response = await client.get(f"{base_url}/api/examples")
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ 获取到 {len(data['examples'])} 个搜索示例")
                for example in data['examples'][:3]:
                    print(f"      - {example['title']}: {example['query']}")
            else:
                print(f"   ❌ 获取示例失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 获取示例异常: {e}")
        
        print()
        
        # 3. 测试基本搜索
        print("3. 测试基本搜索...")
        try:
            response = await client.get(f"{base_url}/api/search?query=sql+injection&page=1&page_size=5")
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ 搜索成功")
                print(f"   📊 总结果数: {data['total_results']}")
                print(f"   📄 当前页: {data['page']}/{data['total_pages']}")
                print(f"   📝 当前页结果: {len(data['results'])}")
                print(f"   ⏱️ 搜索耗时: {data['search_time']:.2f}秒")
                
                if data['results']:
                    first_result = data['results'][0]
                    print(f"   🔍 第一个结果: {first_result['cve_info']['cve_id']}")
                    if first_result.get('summary'):
                        print(f"   📝 有缓存总结")
                    else:
                        print(f"   📝 无缓存总结")
            else:
                print(f"   ❌ 搜索失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 搜索异常: {e}")
        
        print()
        
        # 4. 测试分页
        print("4. 测试分页功能...")
        try:
            # 第一页
            response1 = await client.get(f"{base_url}/api/search?query=buffer+overflow&page=1&page_size=3")
            # 第二页
            response2 = await client.get(f"{base_url}/api/search?query=buffer+overflow&page=2&page_size=3")
            
            if response1.status_code == 200 and response2.status_code == 200:
                data1 = response1.json()
                data2 = response2.json()
                
                print(f"   ✅ 分页测试成功")
                print(f"   📊 总结果数: {data1['total_results']} (两页应该相同)")
                print(f"   📄 第一页: {len(data1['results'])} 个结果")
                print(f"   📄 第二页: {len(data2['results'])} 个结果")
                
                if data1['results'] and data2['results']:
                    print(f"   🔍 第一页第一个: {data1['results'][0]['cve_info']['cve_id']}")
                    print(f"   🔍 第二页第一个: {data2['results'][0]['cve_info']['cve_id']}")
            else:
                print(f"   ❌ 分页测试失败: {response1.status_code}, {response2.status_code}")
        except Exception as e:
            print(f"   ❌ 分页测试异常: {e}")
        
        print()
        
        # 5. 测试缓存统计
        print("5. 测试缓存统计...")
        try:
            response = await client.get(f"{base_url}/api/cache/stats")
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ 缓存统计获取成功")
                print(f"   📊 总缓存数: {data['total_cached']}")
                print(f"   ✅ 有效缓存: {data['valid_cached']}")
                print(f"   ❌ 过期缓存: {data['expired_cached']}")
                print(f"   ⏰ 缓存TTL: {data['cache_ttl_hours']} 小时")
            else:
                print(f"   ❌ 缓存统计失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 缓存统计异常: {e}")
        
        print()

        # 6. 测试 AI 总结功能
        print("6. 测试 AI 总结功能...")
        try:
            # 先搜索一个 CVE
            response = await client.get(f"{base_url}/api/search?query=CVE-2023-12345&page=1&page_size=1")
            if response.status_code == 200:
                data = response.json()
                if data['results']:
                    cve_id = data['results'][0]['cve_info']['cve_id']

                    # 测试生成总结
                    summary_response = await client.get(f"{base_url}/api/summary/{cve_id}")
                    if summary_response.status_code == 200:
                        summary_data = summary_response.json()
                        print(f"   ✅ AI 总结生成成功")
                        print(f"   🤖 模型: {summary_data['model_used']}")
                        print(f"   📝 总结长度: {len(summary_data['summary'])} 字符")
                        print(f"   🔍 CVE: {summary_data['cve_id']}")
                    else:
                        print(f"   ❌ AI 总结生成失败: {summary_response.status_code}")
                else:
                    print(f"   ⚠️ 没有找到测试 CVE")
            else:
                print(f"   ❌ 搜索测试 CVE 失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ AI 总结测试异常: {e}")

        print()
        print("🎉 测试完成!")


if __name__ == "__main__":
    asyncio.run(test_api())
