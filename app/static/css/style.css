/* CVE 搜索服务样式文件 - React + Material-UI 版本 */

body {
    margin: 0;
    font-family: 'Roboto', 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.app-container {
    min-height: 100vh;
    padding: 20px;
}

.header {
    text-align: center;
    color: white;
    margin-bottom: 40px;
}

/* Material Icons 样式 */
.material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}

/* Material-UI 兼容样式 */
.MuiPaper-root {
    transition: all 0.3s ease !important;
}

.MuiCard-root:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

/* 修复筛选器数字溢出问题 */
.MuiBadge-badge {
    font-size: 0.75rem !important;
    min-width: 20px !important;
    height: 20px !important;
    padding: 0 6px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* 改善文字颜色 */
.MuiTypography-root {
    color: #2c3e50 !important;
}

.MuiTypography-h5 {
    color: #e74c3c !important;
}

.MuiTypography-h6 {
    color: #34495e !important;
}

.MuiTypography-body1 {
    color: #555 !important;
    line-height: 1.6 !important;
}

.MuiTypography-body2 {
    color: #666 !important;
}

/* 按钮样式优化 */
.MuiButton-root {
    text-transform: none !important;
    border-radius: 8px !important;
}

.MuiButton-contained {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

.MuiButton-contained:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

/* 自定义样式保持与原设计一致 */
.summary-content {
    line-height: 1.6;
}

.summary-content h1,
.summary-content h2,
.summary-content h3 {
    margin: 10px 0 5px 0;
    color: #2e7d32;
}

.summary-content p {
    margin: 8px 0;
}

.summary-content ul,
.summary-content ol {
    margin: 8px 0;
    padding-left: 20px;
}

.summary-content code {
    background: rgba(0, 0, 0, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

.summary-content blockquote {
    border-left: 4px solid #4caf50;
    margin: 10px 0;
    padding-left: 15px;
    color: #666;
}

/* JSON 悬浮窗口样式 */
.json-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.json-modal-content {
    background: #1e1e1e;
    border-radius: 12px;
    width: 90%;
    max-width: 1200px;
    height: 80%;
    max-height: 800px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.json-modal-header {
    background: #2d2d2d;
    color: white;
    padding: 16px 24px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #404040;
}

.json-modal-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.json-modal-header h3 {
    margin: 0;
    color: #61dafb;
    font-size: 1.2rem;
}

.json-modal-close {
    background: none;
    border: none;
    color: #ccc;
    font-size: 24px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;
}

.json-modal-close:hover {
    background: #404040;
    color: white;
}

.json-modal-body {
    flex: 1;
    overflow: auto;
    padding: 0;
    background: #1e1e1e;
}

.json-viewer {
    height: 100%;
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-size: 14px;
    line-height: 1.4;
}

/* JSON 语法高亮 */
.json-key {
    color: #9cdcfe;
}

.json-string {
    color: #ce9178;
}

.json-number {
    color: #b5cea8;
}

.json-boolean {
    color: #569cd6;
}

.json-null {
    color: #569cd6;
}

.json-punctuation {
    color: #d4d4d4;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-container {
        padding: 10px;
    }

    .header {
        margin-bottom: 20px;
    }

    .json-modal-content {
        width: 95%;
        height: 90%;
        margin: 20px;
    }

    .json-modal-header {
        padding: 12px 16px;
    }

    .json-viewer {
        font-size: 12px;
    }
}
