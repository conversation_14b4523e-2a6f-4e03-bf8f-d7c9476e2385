"""
配置管理模块
负责加载和管理应用配置
"""

import os
import yaml
from pathlib import Path
from typing import Optional
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings


class LLMConfig(BaseModel):
    """LLM 配置"""
    base_url: str = "https://api.deepseek.com"
    api_key: str = "your-deepseek-api-key-here"
    model: str = "deepseek-chat"
    timeout: int = 30
    max_retries: int = 3


class SearchConfig(BaseModel):
    """搜索配置"""
    cve_directory: str = "~/Github/cvelistV5"
    max_results: int = 100
    timeout: int = 300


class DatabaseConfig(BaseModel):
    """数据库配置"""
    url: str = "sqlite+aiosqlite:///./cache.db"
    echo: bool = False


class ServerConfig(BaseModel):
    """服务器配置"""
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False


class CacheConfig(BaseModel):
    """缓存配置"""
    ttl: int = 86400  # 24 小时


class Settings(BaseSettings):
    """应用设置"""
    llm: LLMConfig = Field(default_factory=LLMConfig)
    search: SearchConfig = Field(default_factory=SearchConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    server: ServerConfig = Field(default_factory=ServerConfig)
    cache: CacheConfig = Field(default_factory=CacheConfig)

    class Config:
        env_file = ".env"
        env_nested_delimiter = "__"


def load_config(config_path: Optional[str] = None) -> Settings:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径，默认为 config/config.yaml
        
    Returns:
        Settings: 配置对象
    """
    if config_path is None:
        config_path = Path(__file__).parent.parent.parent / "config" / "config.yaml"
    
    config_path = Path(config_path)
    
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        # 创建设置对象
        settings = Settings(**config_data)
    else:
        # 使用默认配置
        settings = Settings()
    
    # 处理环境变量覆盖
    if os.getenv("DEEPSEEK_API_KEY"):
        settings.llm.api_key = os.getenv("DEEPSEEK_API_KEY")
    
    if os.getenv("CVE_DIRECTORY"):
        settings.search.cve_directory = os.getenv("CVE_DIRECTORY")
    
    # 展开用户目录
    settings.search.cve_directory = os.path.expanduser(settings.search.cve_directory)
    
    return settings


# 全局配置实例
settings = load_config()
