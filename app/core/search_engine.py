"""
CVE 搜索引擎
基于原始 search_files.py 脚本改进的搜索引擎
"""

import os
import re
import json
import subprocess
import asyncio
from typing import List, Set, Optional, Dict, Any
from dataclasses import dataclass
from pathlib import Path
from datetime import datetime

from ..models.schemas import CVEInfo


@dataclass
class SearchTerm:
    """搜索项"""
    is_positive: bool
    content: str
    is_quoted: bool = False


@dataclass
class GroupTerm:
    """分组项 (prefix|option1|option2)"""
    prefix: str
    options: List[str]
    is_positive: bool


class QueryParser:
    """查询语法解析器"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        self.pos = 0
        self.text = ""
        self.length = 0
    
    def parse(self, query: str) -> tuple[List[SearchTerm], List[GroupTerm]]:
        """解析查询语句"""
        self.text = query.strip()
        self.length = len(self.text)
        self.pos = 0
        
        search_terms = []
        group_terms = []
        
        while self.pos < self.length:
            self._skip_whitespace()
            if self.pos >= self.length:
                break
                
            # 解析单个搜索项
            term, group = self._parse_term()
            if term:
                search_terms.append(term)
            if group:
                group_terms.append(group)
            
            # 跳过逗号
            self._skip_whitespace()
            if self.pos < self.length and self.text[self.pos] == ',':
                self.pos += 1
        
        return search_terms, group_terms
    
    def _parse_term(self) -> tuple[Optional[SearchTerm], Optional[GroupTerm]]:
        """解析单个搜索项"""
        self._skip_whitespace()
        
        # 判断极性
        is_positive = True
        if self.pos < self.length and self.text[self.pos] in '+-':
            is_positive = self.text[self.pos] == '+'
            self.pos += 1
        
        self._skip_whitespace()
        
        # 解析引号字符串
        if self.pos < self.length and self.text[self.pos] == '"':
            content = self._parse_quoted()
            return SearchTerm(is_positive, content, True), None
        
        # 解析分组或普通词组
        content = self._parse_phrase_or_group()
        
        if '(' in content and ')' in content:
            return None, self._parse_group_content(content, is_positive)
        else:
            # 将下划线转换为空格
            content = content.replace('_', ' ')
            return SearchTerm(is_positive, content, False), None
    
    def _parse_quoted(self) -> str:
        """解析引号内容"""
        self.pos += 1  # 跳过开始引号
        start = self.pos
        
        while self.pos < self.length and self.text[self.pos] != '"':
            self.pos += 1
        
        content = self.text[start:self.pos]
        if self.pos < self.length:
            self.pos += 1  # 跳过结束引号
        
        return content
    
    def _parse_phrase_or_group(self) -> str:
        """解析词组或分组"""
        start = self.pos
        paren_count = 0
        
        while self.pos < self.length:
            char = self.text[self.pos]
            if char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
            elif char in ',+-' and paren_count == 0:
                break
            self.pos += 1
        
        return self.text[start:self.pos].strip()
    
    def _parse_group_content(self, content: str, is_positive: bool) -> GroupTerm:
        """解析分组内容"""
        # 匹配 prefix(option1|option2|...) 格式
        match = re.match(r'([^(]*)\(([^)]+)\)', content)
        if not match:
            # 如果没有前缀，当作普通词组处理
            return GroupTerm("", [content.replace('_', ' ')], is_positive)
        
        prefix = match.group(1).replace('_', ' ').strip()
        options_str = match.group(2)
        options = [opt.replace('_', ' ').strip() for opt in options_str.split('|')]
        
        return GroupTerm(prefix, options, is_positive)
    
    def _skip_whitespace(self):
        """跳过空白字符"""
        while self.pos < self.length and self.text[self.pos].isspace():
            self.pos += 1


class CVESearchEngine:
    """CVE 搜索引擎"""
    
    def __init__(self, base_dir: str):
        self.base_dir = Path(base_dir)
        self.parser = QueryParser()
    
    async def search(self, query: str, page: int = 1, page_size: int = 50) -> tuple[List[CVEInfo], int]:
        """
        执行搜索

        Returns:
            tuple: (CVE信息列表, 总结果数)
        """
        print(f"解析查询: {query}")

        # 检查是否是精确的 CVE ID 搜索
        if self._is_exact_cve_id(query):
            print(f"检测到精确 CVE ID 搜索: {query}")
            return await self._search_exact_cve_id(query, page, page_size)

        # 解析查询
        search_terms, group_terms = self.parser.parse(query)

        print(f"解析结果:")
        print(f"  普通搜索项: {len(search_terms)}")
        for term in search_terms:
            print(f"    {'包含' if term.is_positive else '排除'}: '{term.content}' {'(引号)' if term.is_quoted else ''}")

        print(f"  分组搜索项: {len(group_terms)}")
        for group in group_terms:
            print(f"    {'包含' if group.is_positive else '排除'}: '{group.prefix}' + ({' | '.join(group.options)})")

        # 第一阶段：ripgrep 粗筛
        candidate_files = await self._ripgrep_filter(search_terms, group_terms)
        print(f"\nripgrep 粗筛结果: {len(candidate_files)} 个候选文件")

        if not candidate_files:
            return [], 0

        # 第二阶段：Python 精确匹配
        final_results = await self._precise_filter(candidate_files, search_terms, group_terms)
        print(f"精确匹配结果: {len(final_results)} 个文件")

        # 解析所有 CVE 信息并按日期排序
        all_cve_infos = []
        for file_path in final_results:
            cve_info = await self._parse_cve_file(file_path)
            if cve_info:
                all_cve_infos.append(cve_info)

        # 按发布日期排序（新到旧）
        all_cve_infos.sort(key=lambda x: x.published_date or "", reverse=True)

        # 计算分页
        total_results = len(all_cve_infos)
        start_index = (page - 1) * page_size
        end_index = start_index + page_size

        # 返回当前页的结果
        page_results = all_cve_infos[start_index:end_index]

        return page_results, total_results

    def _is_exact_cve_id(self, query: str) -> bool:
        """检查是否是精确的 CVE ID"""
        # CVE ID 格式: CVE-YYYY-NNNN 或 CVE-YYYY-NNNNN 等
        import re
        cve_pattern = r'^CVE-\d{4}-\d{4,}$'
        return bool(re.match(cve_pattern, query.strip(), re.IGNORECASE))

    async def _search_exact_cve_id(self, cve_id: str, page: int = 1, page_size: int = 50) -> tuple[List[CVEInfo], int]:
        """精确搜索特定的 CVE ID"""
        print(f"执行精确 CVE ID 搜索: {cve_id}")

        try:
            # 使用 ripgrep 直接搜索 CVE ID
            cmd = [
                'rg',
                '--files-with-matches',
                '--ignore-case',
                '--type', 'json',
                re.escape(cve_id),
                str(self.base_dir)
            ]

            print(f"执行精确搜索命令: {' '.join(cmd)}")

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=60)

            if process.returncode == 0:
                files = [line.strip() for line in stdout.decode().strip().split('\n') if line.strip()]
                print(f"找到 {len(files)} 个候选文件")

                # 解析文件并验证 CVE ID
                matching_cves = []
                for file_path in files:
                    cve_info = await self._parse_cve_file(file_path)
                    if cve_info and cve_info.cve_id.upper() == cve_id.upper():
                        matching_cves.append(cve_info)
                        print(f"找到匹配的 CVE: {cve_info.cve_id} 在文件 {file_path}")

                # 计算分页
                total_results = len(matching_cves)
                start_index = (page - 1) * page_size
                end_index = start_index + page_size
                page_results = matching_cves[start_index:end_index]

                return page_results, total_results
            else:
                print(f"精确搜索未找到结果，返回码: {process.returncode}")
                return [], 0

        except Exception as e:
            print(f"精确 CVE ID 搜索出错: {e}")
            return [], 0
    
    def _extract_keywords(self, search_terms: List[SearchTerm], group_terms: List[GroupTerm]) -> Set[str]:
        """提取所有关键词用于 ripgrep"""
        keywords = set()
        
        # 从普通搜索项提取
        for term in search_terms:
            if term.is_positive:  # 只用正向词进行粗筛
                if term.is_quoted:
                    keywords.add(term.content)
                else:
                    # 将词组拆分为单词
                    words = term.content.split()
                    keywords.update(words)
        
        # 从分组项提取
        for group in group_terms:
            if group.is_positive:  # 只用正向词进行粗筛
                if group.prefix:
                    keywords.update(group.prefix.split())
                for option in group.options:
                    keywords.update(option.split())
        
        return keywords

    async def _ripgrep_filter(self, search_terms: List[SearchTerm], group_terms: List[GroupTerm]) -> List[str]:
        """使用 ripgrep 进行粗筛"""
        keywords = self._extract_keywords(search_terms, group_terms)

        if not keywords:
            print("警告: 没有找到正向关键词，跳过 ripgrep 筛选")
            return []

        print(f"ripgrep 关键词: {list(keywords)}")

        try:
            # 构建 ripgrep 命令
            # 使用所有关键词的OR组合进行搜索
            pattern = '|'.join(re.escape(kw) for kw in keywords)

            cmd = [
                'rg',
                '--files-with-matches',  # 只返回文件名
                '--ignore-case',         # 忽略大小写
                '--type', 'json',
                pattern,
                str(self.base_dir)
            ]

            print(f"执行命令: {' '.join(cmd)}")

            # 使用 asyncio 执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=300)

            if process.returncode == 0:
                files = [line.strip() for line in stdout.decode().strip().split('\n') if line.strip()]
                return files
            elif process.returncode == 1:
                # ripgrep 返回 1 表示没有匹配
                print("ripgrep 没有找到匹配的文件")
                return []
            else:
                print(f"ripgrep 错误 (代码 {process.returncode}): {stderr.decode()}")
                return []

        except asyncio.TimeoutError:
            print("ripgrep 执行超时")
            return []
        except FileNotFoundError:
            print("错误: 找不到 ripgrep 命令，请确保已正确安装")
            return []
        except Exception as e:
            print(f"ripgrep 执行出错: {e}")
            return []

    async def _precise_filter(self, files: List[str], search_terms: List[SearchTerm],
                             group_terms: List[GroupTerm]) -> List[str]:
        """精确匹配过滤"""
        results = []

        for file_path in files:
            try:
                if await self._file_matches(file_path, search_terms, group_terms):
                    results.append(file_path)
            except Exception as e:
                print(f"处理文件 {file_path} 时出错: {e}")
                continue

        return results

    async def _file_matches(self, file_path: str, search_terms: List[SearchTerm],
                           group_terms: List[GroupTerm]) -> bool:
        """检查文件是否匹配所有条件"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read().lower()  # 转为小写进行不区分大小写匹配
        except Exception:
            return False

        # 检查普通搜索项
        for term in search_terms:
            search_content = term.content.lower()
            matches = search_content in content

            if term.is_positive and not matches:
                return False
            if not term.is_positive and matches:
                return False

        # 检查分组项
        for group in group_terms:
            group_matches = False

            for option in group.options:
                if group.prefix:
                    full_term = f"{group.prefix} {option}".lower()
                else:
                    full_term = option.lower()

                if full_term in content:
                    group_matches = True
                    break

            if group.is_positive and not group_matches:
                return False
            if not group.is_positive and group_matches:
                return False

        return True

    async def _parse_cve_file(self, file_path: str) -> Optional[CVEInfo]:
        """解析 CVE JSON 文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 提取 CVE ID
            cve_id = data.get('cveMetadata', {}).get('cveId', '')
            if not cve_id:
                return None

            # 提取基本信息
            containers = data.get('containers', {})
            cna_data = containers.get('cna', {})

            # 提取标题和描述
            title = cna_data.get('title', '')
            description = None

            descriptions = cna_data.get('descriptions', [])
            for desc in descriptions:
                if desc.get('lang') == 'en':
                    description = desc.get('value', '')
                    break

            if not description and descriptions:
                description = descriptions[0].get('value', '')

            # 提取日期信息
            published_date = data.get('cveMetadata', {}).get('datePublished', '')
            modified_date = data.get('cveMetadata', {}).get('dateUpdated', '')

            # 提取 CVSS 信息
            cvss_score = None
            severity = None

            metrics = cna_data.get('metrics', [])
            for metric in metrics:
                if 'cvssV3_1' in metric:
                    cvss_data = metric['cvssV3_1']
                    cvss_score = cvss_data.get('baseScore')
                    severity = cvss_data.get('baseSeverity')
                    break
                elif 'cvssV3_0' in metric:
                    cvss_data = metric['cvssV3_0']
                    cvss_score = cvss_data.get('baseScore')
                    severity = cvss_data.get('baseSeverity')
                    break

            # 提取受影响的产品
            affected_products = []
            affected = cna_data.get('affected', [])
            for item in affected:
                vendor = item.get('vendor', '')
                product = item.get('product', '')
                if vendor and product:
                    affected_products.append(f"{vendor} {product}")
                elif product:
                    affected_products.append(product)

            # 提取参考链接
            references = []
            refs = cna_data.get('references', [])
            for ref in refs:
                url = ref.get('url', '')
                if url:
                    references.append(url)

            return CVEInfo(
                cve_id=cve_id,
                title=title,
                description=description,
                published_date=published_date,
                modified_date=modified_date,
                severity=severity,
                cvss_score=cvss_score,
                affected_products=affected_products,
                references=references,
                file_path=file_path
            )

        except Exception as e:
            print(f"解析 CVE 文件 {file_path} 时出错: {e}")
            return None

    async def get_raw_cve_data(self, file_path: str) -> Optional[Dict[str, Any]]:
        """获取 CVE 文件的原始 JSON 数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        except Exception as e:
            print(f"读取 CVE 原始数据 {file_path} 时出错: {e}")
            return None
