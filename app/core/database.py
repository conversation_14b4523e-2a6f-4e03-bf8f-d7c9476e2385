"""
数据库连接和模型定义
负责 SQLite 数据库的连接和 ORM 模型
"""

from datetime import datetime
from sqlalchemy import Column, String, DateTime, Text, Float, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from .config import settings

# 创建基类
Base = declarative_base()


class CVESummaryCache(Base):
    """CVE 总结缓存表"""
    __tablename__ = "cve_summary_cache"
    
    cve_id = Column(String(20), primary_key=True, index=True, comment="CVE 编号")
    summary = Column(Text, nullable=False, comment="LLM 总结内容")
    generated_at = Column(DateTime, default=datetime.utcnow, comment="生成时间")
    model_used = Column(String(50), nullable=False, comment="使用的模型")
    
    def __repr__(self):
        return f"<CVESummaryCache(cve_id='{self.cve_id}', model='{self.model_used}')>"


class SearchLog(Base):
    """搜索日志表"""
    __tablename__ = "search_log"
    
    id = Column(String(36), primary_key=True, comment="日志 ID")
    query = Column(String(500), nullable=False, index=True, comment="搜索查询")
    results_count = Column(String(10), nullable=False, comment="结果数量")
    search_time = Column(Float, nullable=False, comment="搜索耗时")
    timestamp = Column(DateTime, default=datetime.utcnow, index=True, comment="搜索时间")
    
    def __repr__(self):
        return f"<SearchLog(query='{self.query}', results={self.results_count})>"


# 创建异步引擎
engine = create_async_engine(
    settings.database.url,
    echo=settings.database.echo,
    future=True
)

# 创建异步会话工厂
AsyncSessionLocal = sessionmaker(
    engine, 
    class_=AsyncSession, 
    expire_on_commit=False
)


async def init_database():
    """初始化数据库，创建所有表"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


async def get_database():
    """获取数据库会话的依赖注入函数"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


async def close_database():
    """关闭数据库连接"""
    await engine.dispose()
