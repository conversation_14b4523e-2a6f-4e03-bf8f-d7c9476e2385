"""
API 路由定义
定义所有的 API 端点
"""

import time
import os
from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.database import get_database
from ..core.config import settings
from ..core.search_engine import CVESearchEngine
from ..services.cache_service import CacheService
from ..models.schemas import (
    SearchRequest, SearchResponse, CVEDetail, HealthResponse,
    SummaryRequest, ErrorResponse, SearchExamplesResponse, SearchExample
)

# 创建路由器
router = APIRouter(prefix="/api", tags=["CVE Search API"])

# 初始化服务
search_engine = CVESearchEngine(settings.search.cve_directory)
cache_service = CacheService()


@router.get("/health", response_model=HealthResponse, summary="健康检查")
async def health_check(db: AsyncSession = Depends(get_database)):
    """
    健康检查端点
    检查服务状态、CVE 目录和数据库连接
    """
    try:
        # 检查 CVE 目录是否存在
        cve_directory_exists = os.path.exists(settings.search.cve_directory)
        
        # 检查数据库连接
        database_connected = True
        try:
            await db.execute("SELECT 1")
        except Exception:
            database_connected = False
        
        return HealthResponse(
            status="healthy" if cve_directory_exists and database_connected else "unhealthy",
            timestamp=time.time(),
            version="1.0.0",
            cve_directory_exists=cve_directory_exists,
            database_connected=database_connected
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@router.get("/search", response_model=SearchResponse, summary="搜索 CVE")
async def search_cves(
    query: str = Query(..., description="搜索查询语句", min_length=1),
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(50, description="每页结果数", ge=1, le=100),
    db: AsyncSession = Depends(get_database)
):
    """
    搜索 CVE 漏洞

    支持复杂的搜索语法：
    - `term1, term2` - 包含所有词汇
    - `+term` - 必须包含
    - `-term` - 必须不包含
    - `"exact phrase"` - 精确短语
    - `prefix(option1|option2)` - 分组搜索
    """
    try:
        start_time = time.time()

        # 执行搜索
        cve_infos, total_results = await search_engine.search(query, page, page_size)

        # 构建详细结果，只包含基本信息，不包含总结
        results = []
        for cve_info in cve_infos:
            cve_detail = CVEDetail(cve_info=cve_info)

            # 检查是否有缓存的总结（不生成新的）
            cached_summary = await cache_service.get_cached_summary_only(cve_info.cve_id, db)
            if cached_summary:
                cve_detail.summary = cached_summary

            results.append(cve_detail)

        search_time = time.time() - start_time

        # 计算总页数
        total_pages = (total_results + page_size - 1) // page_size

        # 记录搜索日志
        await cache_service.log_search(query, total_results, search_time, db)

        return SearchResponse(
            query=query,
            total_results=total_results,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            results=results,
            search_time=search_time
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@router.get("/cve/{cve_id}", response_model=CVEDetail, summary="获取特定 CVE 详情")
async def get_cve_detail(
    cve_id: str,
    db: AsyncSession = Depends(get_database)
):
    """
    获取特定 CVE 的详细信息
    """
    try:
        # 搜索特定的 CVE，使用新的分页接口
        cve_infos, total = await search_engine.search(cve_id, page=1, page_size=1)

        if not cve_infos:
            raise HTTPException(status_code=404, detail=f"未找到 CVE: {cve_id}")

        cve_info = cve_infos[0]
        cve_detail = CVEDetail(cve_info=cve_info)

        # 检查是否有缓存的总结
        cached_summary = await cache_service.get_cached_summary_only(cve_info.cve_id, db)
        if cached_summary:
            cve_detail.summary = cached_summary

        return cve_detail

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取 CVE 详情失败: {str(e)}")


@router.get("/summary/{cve_id}", summary="获取 CVE 总结")
async def get_cve_summary(
    cve_id: str,
    force_refresh: bool = Query(False, description="是否强制刷新缓存"),
    db: AsyncSession = Depends(get_database)
):
    """
    获取特定 CVE 的 LLM 总结（异步生成）
    """
    try:
        # 先搜索 CVE 信息，使用新的分页接口
        cve_infos, total = await search_engine.search(cve_id, page=1, page_size=1)

        if not cve_infos:
            raise HTTPException(status_code=404, detail=f"未找到 CVE: {cve_id}")

        cve_info = cve_infos[0]

        # 获取或生成总结
        summary = await cache_service.get_or_generate_summary(
            cve_info, db, force_refresh=force_refresh
        )

        if not summary:
            raise HTTPException(status_code=500, detail="无法生成 CVE 总结")

        return summary

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取总结失败: {str(e)}")


@router.post("/summary", summary="生成或获取 CVE 总结")
async def get_or_generate_summary(
    request: SummaryRequest,
    db: AsyncSession = Depends(get_database)
):
    """
    获取或生成特定 CVE 的 LLM 总结
    """
    try:
        # 先搜索 CVE 信息，使用新的分页接口
        cve_infos, total = await search_engine.search(request.cve_id, page=1, page_size=1)

        if not cve_infos:
            raise HTTPException(status_code=404, detail=f"未找到 CVE: {request.cve_id}")

        cve_info = cve_infos[0]

        # 获取或生成总结
        summary = await cache_service.get_or_generate_summary(
            cve_info, db, force_refresh=request.force_refresh
        )

        if not summary:
            raise HTTPException(status_code=500, detail="无法生成 CVE 总结")

        return summary

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取总结失败: {str(e)}")


@router.get("/examples", response_model=SearchExamplesResponse, summary="获取搜索示例")
async def get_search_examples():
    """
    获取搜索语法示例
    """
    examples = [
        SearchExample(
            title="基本搜索",
            query="remote code execution",
            description="搜索包含 'remote code execution' 的 CVE"
        ),
        SearchExample(
            title="精确短语搜索",
            query='"buffer overflow"',
            description="搜索包含精确短语 'buffer overflow' 的 CVE"
        ),
        SearchExample(
            title="多条件搜索",
            query="apache, +vulnerability, -denial",
            description="包含 'apache' 和 'vulnerability'，但不包含 'denial' 的 CVE"
        ),
        SearchExample(
            title="分组搜索",
            query="microsoft(windows|office|exchange)",
            description="搜索 Microsoft 的 Windows、Office 或 Exchange 相关漏洞"
        ),
        SearchExample(
            title="复合搜索",
            query='sql injection, +mysql, -"denial of service"',
            description="SQL 注入相关，包含 MySQL，但排除拒绝服务攻击"
        ),
        SearchExample(
            title="特定 CVE",
            query="CVE-2023-12345",
            description="搜索特定的 CVE 编号"
        )
    ]
    
    return SearchExamplesResponse(examples=examples)


@router.get("/cache/stats", summary="获取缓存统计")
async def get_cache_stats(db: AsyncSession = Depends(get_database)):
    """
    获取缓存统计信息
    """
    try:
        stats = await cache_service.get_cache_stats(db)
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缓存统计失败: {str(e)}")


@router.post("/cache/cleanup", summary="清理过期缓存")
async def cleanup_cache(db: AsyncSession = Depends(get_database)):
    """
    清理过期的缓存条目
    """
    try:
        deleted_count = await cache_service.cleanup_expired_cache(db)
        return {"message": f"已清理 {deleted_count} 个过期缓存条目"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理缓存失败: {str(e)}")


@router.get("/cve/{cve_id}/raw", summary="获取 CVE 原始 JSON 数据")
async def get_cve_raw_data(cve_id: str):
    """
    获取特定 CVE 的原始 JSON 数据
    用于在前端悬浮窗口中显示完整的 CVE 信息
    """
    try:
        # 搜索特定的 CVE
        cve_infos, total = await search_engine.search(cve_id, page=1, page_size=1)

        if not cve_infos:
            raise HTTPException(status_code=404, detail=f"未找到 CVE: {cve_id}")

        cve_info = cve_infos[0]

        # 获取原始 JSON 数据
        raw_data = await search_engine.get_raw_cve_data(cve_info.file_path)

        if not raw_data:
            raise HTTPException(status_code=500, detail=f"无法读取 CVE 原始数据: {cve_id}")

        return {
            "cve_id": cve_id,
            "file_path": cve_info.file_path,
            "raw_data": raw_data
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取 CVE 原始数据失败: {str(e)}")
