"""
数据模型定义
定义 API 请求和响应的数据结构
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class SearchRequest(BaseModel):
    """搜索请求模型"""
    query: str = Field(..., description="搜索查询语句", min_length=1)
    page: int = Field(1, description="页码", ge=1)
    page_size: int = Field(50, description="每页结果数", ge=1, le=100)
    include_summary: bool = Field(True, description="是否包含 LLM 总结")


class CVEInfo(BaseModel):
    """CVE 信息模型"""
    cve_id: str = Field(..., description="CVE 编号")
    title: Optional[str] = Field(None, description="漏洞标题")
    description: Optional[str] = Field(None, description="漏洞描述")
    published_date: Optional[str] = Field(None, description="发布日期")
    modified_date: Optional[str] = Field(None, description="修改日期")
    severity: Optional[str] = Field(None, description="严重程度")
    cvss_score: Optional[float] = Field(None, description="CVSS 评分")
    affected_products: List[str] = Field(default_factory=list, description="受影响的产品")
    references: List[str] = Field(default_factory=list, description="参考链接")
    file_path: str = Field(..., description="文件路径")


class CVESummary(BaseModel):
    """CVE 总结模型"""
    cve_id: str = Field(..., description="CVE 编号")
    summary: str = Field(..., description="LLM 总结内容")
    generated_at: datetime = Field(..., description="生成时间")
    model_used: str = Field(..., description="使用的模型")

    model_config = {"protected_namespaces": ()}


class CVEDetail(BaseModel):
    """CVE 详细信息模型"""
    cve_info: CVEInfo
    summary: Optional[CVESummary] = None
    raw_data: Optional[Dict[str, Any]] = Field(None, description="原始 JSON 数据")


class SearchResponse(BaseModel):
    """搜索响应模型"""
    query: str = Field(..., description="搜索查询")
    total_results: int = Field(..., description="总结果数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页结果数")
    total_pages: int = Field(..., description="总页数")
    results: List[CVEDetail] = Field(..., description="当前页搜索结果")
    search_time: float = Field(..., description="搜索耗时（秒）")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误信息")
    detail: Optional[str] = Field(None, description="详细错误信息")
    code: Optional[str] = Field(None, description="错误代码")


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    timestamp: datetime = Field(..., description="检查时间")
    version: str = Field(..., description="服务版本")
    cve_directory_exists: bool = Field(..., description="CVE 目录是否存在")
    database_connected: bool = Field(..., description="数据库是否连接")


class SummaryRequest(BaseModel):
    """总结请求模型"""
    cve_id: str = Field(..., description="CVE 编号")
    force_refresh: bool = Field(False, description="是否强制刷新缓存")


class SearchExample(BaseModel):
    """搜索示例模型"""
    title: str = Field(..., description="示例标题")
    query: str = Field(..., description="搜索查询")
    description: str = Field(..., description="示例描述")


class SearchExamplesResponse(BaseModel):
    """搜索示例响应模型"""
    examples: List[SearchExample] = Field(..., description="搜索示例列表")
